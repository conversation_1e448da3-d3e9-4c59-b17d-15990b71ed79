<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { RouterView, useRoute } from 'vue-router'
import HeaderBar from './components/HeaderBar.vue'
import TaskHistory from './components/TaskHistory.vue'
import AppFooter from './components/AppFooter.vue'
import SystemErrorMask from './components/common/SystemErrorMask.vue'
import TaskNameModal from './components/common/TaskNameModal.vue'
import { useStates } from './store/states'
import { storeToRefs } from 'pinia'
import { collectPageState } from './utils/pageState'
import {
  getAuthToken,
  getTokenExpireTime,
  getUserInfo,
  isAuthenticated,
  clearAuthData,
} from './utils/auth'

const isHistoryPanelOpen = ref(false)
const route = useRoute()

// Determine whether to show navigation elements (header, footer, task history)
// Hide navigation on login page and 404 error pages for security
const shouldShowNavigation = computed(() => {
  return route.path !== '/login' && !route.matched.some(record => record.path === '/:pathMatch(.*)')
})

// Task name modal state
const statesStore = useStates()
const { taskName } = storeToRefs(statesStore)
const showTaskNameModal = ref(false)
const isClosingBrowser = ref(false)

const toggleHistoryPanel = () => {
  isHistoryPanelOpen.value = !isHistoryPanelOpen.value
}

const closeHistoryPanel = () => {
  isHistoryPanelOpen.value = false
}

const handleViewTask = (task) => {
  console.log('View task:', task)
  // Handle viewing task details
}

const handleRerunTask = (task) => {
  console.log('Rerun task:', task)
  // Handle rerunning task
}

// Browser close event handler
const handleBeforeUnload = (event) => {
  // Only show modal on guided pages with unsaved changes
  if (route.path === '/' && statesStore.pageName === '引导式') {
    event.preventDefault()
    event.returnValue = '' // Required for Chrome

    // Show task name modal
    isClosingBrowser.value = true
    showTaskNameModal.value = true

    return '您有未保存的任务，是否要保存？'
  }
}

// Task name modal handlers
const handleTaskNameConfirm = async (newTaskName) => {
  try {
    // Update task name in store
    statesStore.setTaskName(newTaskName)

    // Save task data
    const allinfo = {
      ...collectPageState(),
      taskName: newTaskName,
      timestamp: new Date().getTime(),
    }

    const response = await fetch('http://localhost:5000/save_allinfo', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ allinfo }),
    })

    if (response.ok) {
      alert(`任务 "${newTaskName}" 保存成功`)
    } else {
      throw new Error('保存失败')
    }
  } catch (error) {
    console.error('保存失败:', error)
    alert('保存失败: ' + error.message)
  } finally {
    showTaskNameModal.value = false
    isClosingBrowser.value = false
  }
}

const handleTaskNameCancel = () => {
  showTaskNameModal.value = false
  isClosingBrowser.value = false
}

// Authentication state restoration
const restoreAuthState = () => {
  console.log('Restoring authentication state from localStorage')

  try {
    const token = getAuthToken()
    const expireTime = getTokenExpireTime()
    const userInfo = getUserInfo()

    if (token && expireTime && userInfo) {
      // Check if token is still valid
      if (isAuthenticated()) {
        console.log('Valid authentication data found, restoring to store')

        // Restore authentication data to store
        statesStore.setAuthToken(token)
        statesStore.setTokenExpireTime(expireTime)
        statesStore.setUserInfo(userInfo)
        statesStore.setUsername(userInfo.username || '')
        statesStore.setUserRole(userInfo.role || '')

        console.log('Authentication state restored successfully')
      } else {
        console.log('Token expired, clearing authentication data')
        clearAuthData()
        statesStore.clearAllAuthData()
      }
    } else {
      console.log('No valid authentication data found in localStorage')
    }
  } catch (error) {
    console.error('Error restoring authentication state:', error)
    // Clear potentially corrupted data
    clearAuthData()
    statesStore.clearAllAuthData()
  }
}

// Lifecycle hooks
onMounted(() => {
  // Restore authentication state on app initialization
  restoreAuthState()

  window.addEventListener('beforeunload', handleBeforeUnload)
})

onUnmounted(() => {
  window.removeEventListener('beforeunload', handleBeforeUnload)
})
</script>

<template>
  <div class="layout-wrapper">
    <HeaderBar v-if="shouldShowNavigation" @toggle-history="toggleHistoryPanel" />
    <TaskHistory
      v-if="shouldShowNavigation"
      :is-open="isHistoryPanelOpen"
      @close="closeHistoryPanel"
      @view-task="handleViewTask"
      @rerun-task="handleRerunTask"
    />
    <main class="container">
      <RouterView />
    </main>
    <AppFooter v-if="shouldShowNavigation" />

    <!-- Global System Error Mask -->
    <SystemErrorMask />

    <!-- Global Task Name Modal for browser close -->
    <TaskNameModal
      :show="showTaskNameModal"
      :initial-value="taskName"
      @confirm="handleTaskNameConfirm"
      @cancel="handleTaskNameCancel"
    />
  </div>
</template>

<style scoped>
.layout-wrapper {
  display: flex;
  flex-direction: column;
  width: 100vw;
  min-height: 100vh;
  background: #f7faff;
}

.container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: flex-start;
  gap: 2.5rem;
  padding: 5px 0;
  width: 100%;
  flex: 1;
}

@media (max-width: 1300px) {
  .container {
    flex-direction: column;
    align-items: center;
  }
}
</style>

<style>
html,
body,
#app {
  height: 100%;
  margin: 0;
  padding: 0;
}
</style>
